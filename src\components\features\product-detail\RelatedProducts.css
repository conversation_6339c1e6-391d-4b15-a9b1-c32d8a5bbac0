@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.related-products-grid {
  display: grid;
  gap: 1rem;
  transition: all 0.5s ease-in-out;
}

.related-products-grid.expanded {
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
}

.related-products-grid.collapsed {
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
}

@media (min-width: 640px) {
  .related-products-grid.collapsed {
    grid-template-columns: repeat(3, 1fr);
  }
}

@media (min-width: 768px) {
  .related-products-grid.collapsed {
    grid-template-columns: repeat(4, 1fr);
  }
}

@media (min-width: 1024px) {
  .related-products-grid.collapsed {
    grid-template-columns: repeat(6, 1fr);
  }
  
  .related-products-grid.expanded {
    grid-template-columns: repeat(5, 1fr);
  }
}

@media (min-width: 1280px) {
  .related-products-grid.expanded {
    grid-template-columns: repeat(6, 1fr);
  }
}

.product-card-enter {
  opacity: 0;
  transform: translateY(20px) scale(0.95);
}

.product-card-enter-active {
  opacity: 1;
  transform: translateY(0) scale(1);
  transition: all 0.3s ease-out;
}

.loading-shimmer {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}
