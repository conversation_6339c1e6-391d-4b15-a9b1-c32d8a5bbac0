{"name": "mock-project", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@fortawesome/fontawesome-svg-core": "^7.0.0", "@fortawesome/free-brands-svg-icons": "^7.0.0", "@fortawesome/free-solid-svg-icons": "^7.0.0", "@fortawesome/react-fontawesome": "^0.2.3", "@hookform/resolvers": "^5.2.1", "@tailwindcss/line-clamp": "^0.4.4", "axios": "^1.11.0", "lucide-react": "^0.536.0", "react": "^19.1.0", "react-dom": "^19.1.0", "react-hook-form": "^7.62.0", "react-router-dom": "^7.7.1", "zod": "^4.0.15", "zustand": "^5.0.7"}, "devDependencies": {"@eslint/js": "^9.30.1", "@tailwindcss/typography": "^0.5.16", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@vitejs/plugin-react": "^4.6.0", "autoprefixer": "^10.4.21", "eslint": "^9.30.1", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.3.0", "postcss": "^8.5.6", "tailwindcss": "^3.4.17", "typescript": "~5.8.3", "typescript-eslint": "^8.35.1", "vite": "^7.0.4"}}